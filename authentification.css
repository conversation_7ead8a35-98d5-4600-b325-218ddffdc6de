* {
   -webkit-box-sizing: border-box;
   -moz-box-sizing: border-box;
   box-sizing: border-box;
}

a {
   text-decoration: none;
}

body {
   margin: 0;
   padding: 0;
   text-decoration: none;
   list-style: none;
   font-family: 'Inter', sans-serif;

}

:root {
   --lightgray: #71717A;
   --colorblue: rgba(0, 68, 255, 0.75);
}

header {
   width: 100%;
   margin: 0;
   padding: 0;
   position: relative;
   box-shadow: 0px 0.5px 6px 0px rgba(128, 128, 128, 0.748);
}

.container {
   width: 100%;
   padding-left: 3%;
   padding-right: 3%;
   margin-left: auto;
   margin-right: auto;
}

section {
   width: 100%;
   margin: 0;
   padding: 0;
   position: relative;
}


.links-boutons {
   display: flex;
   background: rgba(211, 211, 211, 0.124);
   width: max-content;
   padding: 5px 10px;
   gap: 20px;
   margin-top: 30px;
   border-radius: 4px;
   ;
}

.links-boutons a {
   display: block;
   color: var(--colorblue);
   padding: 10px 80px;
   border: 1px solid var(--colorblue);
   font-size: 14px;
   font-weight: 500;
   border-radius: 8px;
}

.links-boutons a.button-active {
   background-color: var(--colorblue);
   color: white;
}

.description {
   display: flex;
   flex-direction: column;
   padding: 20px;

}

.description h2 {
   padding-left: 30px;
   font-size: 28px;
   font-weight: 600;
   letter-spacing: -0.6px;
}

.description p {
   font-size: 14px;
   font-weight: 400;
   color: var(--lightgray);
   margin: 0;
}

.information {
   display: flex;
   margin: auto;
   width: 75%;
   border-radius: 4px;
   box-sizing: border-box;
   padding: 20px;
   box-shadow: 0px 0.5px 6px 0px rgba(128, 128, 128, 0.748);

}

.form-error {
   color: red;
}

.form-group {
   display: flex;
   flex-direction: column;
   gap: 15px;
   padding: 20px;
   width: 100%;
}

.form-group input {
   width: 100%;
   padding: 12px;
   border: #71717a33 1px solid;
   border-radius: 8px;
   font-size: 18px;
   font-weight: 500;
}

.form-group label {
   font-size: 15px;
   font-weight: 500;
}

.form-group .custom-file-upload {
   position: relative;
   display: inline-block;
   width: 100%;
   height: 50px;
   overflow: hidden;
   border-radius: 8px;
   background-color: transparent;
   border: 1px solid #71717a33;
   display: flex;
   align-items: center;
   justify-content: space-between;
   padding: 0 12px;
   font-size: 16px;
   color: #71717a;
   font-weight: normal;
   cursor: pointer;
}

.form-group .custom-button-upload {
   cursor: pointer;
   width: 200px;
   background-color: #2eb450;
   color: white;
   padding: 10px 20px;
   border: none;
   border-radius: 8px;
   text-align: center;
   line-height: 1;
}

select.role {
   WIDTH: 200PX;
   PADDING: 10PX;
   FONT-SIZE: 14PX;
   FONT-WEIGHT: 600;
}

span {
   display: block;
   width: 600px;
   height: 231px;
   border-radius: 49% 51% 49% 51% / 46% 49% 51% 54%;
   background: rgba(152, 207, 244, 0.1);
}

.question {
   margin: auto;
   font-weight: 100;
   margin: 10px auto;
}

.question a {
   color: green;
}

footer {
   width: 100%;
   height: 55px;
   background-color: #f8f9fa;
   display: flex;
   align-items: center;
   justify-content: center;
   margin-top: 20px;
border-bottom: 1px solid lightslategray;
}

/* .copyright {
   margin-top: 21px;
   display: block;
   border-top: 1px solid rgb(3 3 3 / 19%);
   text-align: center;
   padding: 20px 0;
   color: var(--lightgray);
}  */

/* =========CONNEXION======== */

.connexion {
   width: 100vw;
   margin: 50px 0;

}

.title h2 {
   font-size: 28px;
   font-weight: 600;
   letter-spacing: -0.6px;
   margin-bottom: 10px;

}

.title p {
   font-size: 14px;
   font-weight: 300;
   color: #71717A;
   margin: auto;

}

.info {
   display: flex;
   align-items: center;
   justify-content: center;
   gap: 100px;
   margin: 35px 0;
}

.picture img {
   width: 600px;
}

.connecte {
   display: flex;
   flex-direction: column;
   gap: 27px;
   box-shadow: 3px -4px 8px 0px grey;
   padding: 20px 70px;
}

.info form {
   display: flex;
   flex-direction: column;
   gap: 15px;
}

.connecte label {
   font-size: 16px;
   font-weight: 400;
}

.connecte input {
   padding: 10px;
   border: 0.5px solid lightgrey;
   border-radius: 5px;
   font-size: 16px;
}

input[type="submit"] {
   background-color: var(--colorblue);
   padding: 9px 38px;
   width: max-content;
   color: white;
   margin: auto;
   border: none;
   font-size: 17px;
   border-radius: 8px;
}